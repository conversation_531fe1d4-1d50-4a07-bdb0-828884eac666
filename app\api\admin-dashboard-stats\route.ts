import { NextRequest, NextResponse } from 'next/server';
import { getConnection } from '@/lib/database-config';

export async function GET(request: NextRequest) {
  let connection;
  
  try {
    connection = await getConnection();

    // إحصائيات المنتجات
    const [productsResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured
      FROM products 
      WHERE deleted_at IS NULL
    `);

    // إحصائيات الفئات
    const [categoriesResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
      FROM categories 
      WHERE deleted_at IS NULL
    `);

    // إحصائيات الفئات الفرعية
    const [subcategoriesResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active
      FROM subcategories 
      WHERE deleted_at IS NULL
    `);

    // أحدث المنتجات
    const [recentProductsResult] = await connection.execute(`
      SELECT 
        id,
        title,
        title_ar,
        price,
        is_active,
        is_featured,
        created_at
      FROM products 
      WHERE deleted_at IS NULL
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    // إحصائيات أساسية لطلبات التسعير
    const quoteStats = {
      total: 0,
      pending: 0,
      processed: 0,
      today: 0,
      this_week: 0
    };

    // إحصائيات النظام
    const systemStats = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      platform: process.platform,
      nodeVersion: process.version
    };

    const stats = {
      products: Array.isArray(productsResult) ? productsResult[0] : { total: 0, active: 0, featured: 0 },
      categories: Array.isArray(categoriesResult) ? categoriesResult[0] : { total: 0, active: 0 },
      subcategories: Array.isArray(subcategoriesResult) ? subcategoriesResult[0] : { total: 0, active: 0 },
      quotes: quoteStats,
      recentProducts: recentProductsResult,
      system: systemStats
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Dashboard Stats API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
